{"agent_id": "master_strategist_agent", "agent_name": "Master Strategy Coordinator", "version": "2.0.0", "description": "<PERSON><PERSON><PERSON> a<PERSON> koordine eden ana strateji uzmanı", "model_config": {"primary_model": "gemini-1.5-pro", "fallback_model": "claude-3-sonnet", "temperature": 0.2, "max_tokens": 4096, "top_p": 0.9, "frequency_penalty": 0.1, "presence_penalty": 0.1}, "system_prompt": "Sen bir kripto para master stratejistisin. G<PERSON><PERSON>vin tüm ajanlardan gelen verileri sentezleyerek kapsamlı trading stratejileri geliştirmektir.\n\nVERİ KAYNAKLARI:\n1. Technical Analyst verileri\n2. Sentiment Analyst verileri\n3. Risk Manager değerlendirmeleri\n4. Data Harvester anomali raporları\n\nÇIKTI FORMATI:\n{\n  \"strategy_id\": \"unique_strategy_id\",\n  \"market_scenario\": \"Mevcut piyasa durumu analizi\",\n  \"confidence_level\": 0.85,\n  \"primary_recommendation\": {\n    \"action\": \"BUY|SELL|HOLD\",\n    \"symbols\": [\"BTCUSDT\"],\n    \"allocation\": 0.25,\n    \"timeframe\": \"4h-1d\",\n    \"target_price\": 45000,\n    \"stop_loss\": 42000\n  },\n  \"alternative_scenarios\": [\n    {\n      \"condition\": \"BTC breaks 46000\",\n      \"action\": \"STRONG_BUY\",\n      \"probability\": 0.35\n    }\n  ],\n  \"risk_assessment\": {\n    \"max_drawdown\": 0.08,\n    \"risk_reward_ratio\": 2.5,\n    \"position_size\": 0.15\n  },\n  \"conflicting_signals\": [],\n  \"market_regime\": \"TRENDING|RANGING|VOLATILE\",\n  \"next_review_time\": \"2025-01-21T15:00:00Z\",\n  \"reasoning\": \"Kapsamlı strateji açıklaması\"\n}\n\nKURALLAR:\n- Tüm ajanların verilerini dikkate al\n- Çelişkili sinyalleri belirt\n- Risk-reward oranını optimize et\n- Zaman çerçevelerini netleştir", "tools": [{"name": "strategy_synthesizer", "description": "Çoklu veri kaynağı sentez sistemi", "parameters": {"synthesis_methods": ["weighted_average", "consensus", "majority_vote"], "confidence_weighting": true, "conflict_resolution": "conservative"}}, {"name": "conflict_resolver", "description": "Çelişkili sinyal <PERSON>ü<PERSON>leme", "parameters": {"resolution_strategies": ["priority_based", "confidence_based", "time_based"], "escalation_threshold": 0.3}}], "memory_config": {"short_term_memory": 500, "long_term_memory": 5000, "context_retention": "168h", "learning_enabled": true, "pattern_recognition": true, "strategy_history": true}, "data_integration": {"agent_weights": {"technical_analyst": 0.3, "sentiment_analyst": 0.25, "risk_manager": 0.35, "data_harvester": 0.1}, "confidence_multipliers": {"high_confidence": 1.2, "medium_confidence": 1.0, "low_confidence": 0.7}, "time_decay": {"enabled": true, "half_life_hours": 4, "min_weight": 0.1}}, "strategy_generation": {"market_regimes": {"trending": {"indicators": ["strong_directional_move", "high_volume", "momentum"], "strategies": ["trend_following", "momentum", "breakout"]}, "ranging": {"indicators": ["sideways_movement", "support_resistance", "low_volatility"], "strategies": ["mean_reversion", "range_trading", "scalping"]}, "volatile": {"indicators": ["high_volatility", "news_driven", "uncertainty"], "strategies": ["volatility_trading", "hedging", "reduced_exposure"]}}, "timeframe_analysis": {"short_term": {"period": "1h-4h", "weight": 0.2, "focus": "entry_timing"}, "medium_term": {"period": "4h-1d", "weight": 0.5, "focus": "main_strategy"}, "long_term": {"period": "1d-1w", "weight": 0.3, "focus": "trend_direction"}}}, "decision_framework": {"consensus_threshold": 0.7, "minimum_confidence": 0.6, "conflict_tolerance": 0.2, "risk_adjustment": {"high_risk_periods": 0.5, "medium_risk_periods": 0.75, "low_risk_periods": 1.0}}, "scenario_planning": {"primary_scenario": {"probability_threshold": 0.6, "confidence_requirement": 0.7}, "alternative_scenarios": {"max_scenarios": 3, "min_probability": 0.2, "probability_sum_check": true}, "black_swan_events": {"monitoring": true, "indicators": ["extreme_volatility", "correlation_breakdown", "liquidity_crisis"], "response": "defensive"}}, "portfolio_optimization": {"allocation_methods": {"equal_weight": false, "market_cap_weight": false, "risk_parity": true, "kelly_criterion": true}, "rebalancing": {"frequency": "daily", "threshold": 0.05, "method": "threshold_based"}, "constraints": {"max_single_asset": 0.4, "min_cash_reserve": 0.1, "max_correlation": 0.8}}, "performance_tracking": {"metrics": {"sharpe_ratio": true, "sortino_ratio": true, "max_drawdown": true, "win_rate": true, "profit_factor": true}, "benchmarks": ["BTC", "ETH", "crypto_index"], "reporting_frequency": "daily"}, "alert_conditions": {"high_confidence_opportunity": {"threshold": 0.85, "min_risk_reward": 2.0}, "risk_warning": {"risk_score_threshold": 0.8, "volatility_threshold": 0.6}, "strategy_change": {"regime_change": true, "confidence_drop": 0.3}}, "output_schema": {"type": "object", "required": ["strategy_id", "market_scenario", "confidence_level", "primary_recommendation"], "properties": {"confidence_level": {"type": "number", "minimum": 0, "maximum": 1}, "primary_recommendation": {"type": "object", "required": ["action", "symbols"], "properties": {"action": {"type": "string", "enum": ["STRONG_BUY", "BUY", "HOLD", "SELL", "STRONG_SELL"]}}}, "market_regime": {"type": "string", "enum": ["TRENDING", "RANGING", "VOLATILE"]}}}, "performance_metrics": {"target_response_time": "8s", "target_accuracy": 0.8, "target_uptime": 0.99, "error_threshold": 0.02}, "scheduling": {"frequency": "15m", "retry_attempts": 3, "retry_delay": "120s", "timeout": "90s", "emergency_trigger": true}}