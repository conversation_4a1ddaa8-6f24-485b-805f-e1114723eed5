# 🆓 Ücretsiz AI Provider'lar ile Multi-Agent Trading Assistant

**Güncelleme:** OpenAI yerine ücretsiz AI provider'ları kullanacak şekilde sistem güncellendi!

## 🎯 Ücretsiz AI Provider Seçenekleri

### 1. 🥇 Google Gemini (ÖNERİLEN)
- **Model:** Gemini 1.5 Flash / Pro
- **Ücretsiz Limit:** Günde 1,500 istek
- **Avantajlar:** Hızlı, güvenilir, Türkçe desteği mükemmel
- **API Key:** https://aistudio.google.com/app/apikey

### 2. 🚀 Mistral AI
- **Model:** Mistral Small / Medium
- **Ücretsiz Limit:** Aylık $5 kredi
- **Avantajlar:** Avrupa merkezli, GDPR uyumlu
- **API Key:** https://console.mistral.ai/

### 3. 🌐 OpenRouter
- **Model:** <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> modelleri
- **Ücretsiz Limit:** Günlük limitler var
- **Avantajlar:** Çoklu model erişimi
- **API Key:** https://openrouter.ai/keys

### 4. ⚡ Groq
- **Model:** Llama 3.1, Mixtral
- **Ücretsiz Limit:** Günde 14,400 token
- **Avantajlar:** Çok hızlı inference
- **API Key:** https://console.groq.com/keys

## 🛠️ Güncellenmiş Workflow'lar

### Yeni AI Destekli Workflow'lar:
1. **📰 Sentiment Analyst (Free AI)** - ID: `8WBuBg19eCrQVnYm`
   - Google Gemini ile duygu analizi
   - Mistral fallback desteği
   - Türkçe prompt'lar

2. **🧠 Master Strategist (Free AI)** - ID: `vW30vnfy5LRqpgkH`
   - Google Gemini Pro ile strateji sentezi
   - OpenRouter fallback desteği
   - Gelişmiş Türkçe analiz

### Mevcut Workflow'lar (Değişmeden Kalacak):
- 🕵️ Data Harvester Agent
- 📈 Technical Analyst Agent
- 🛡️ Risk Manager Agent
- 💬 Telegram Herald Assistant

## 🔧 Kurulum Adımları

### 1. API Anahtarlarını Alın

#### Google Gemini (Önerilen):
1. https://aistudio.google.com/app/apikey adresine gidin
2. Google hesabınızla giriş yapın
3. "Create API Key" butonuna tıklayın
4. API anahtarını kopyalayın

#### Mistral AI (Yedek):
1. https://console.mistral.ai/ adresine gidin
2. Hesap oluşturun
3. API Keys sekmesine gidin
4. Yeni anahtar oluşturun

### 2. Environment Dosyasını Güncelleyin

```bash
# .env dosyasını düzenleyin
nano .env

# Aşağıdaki satırları ekleyin:
GOOGLE_GEMINI_API_KEY=your_google_gemini_api_key_here
MISTRAL_AI_API_KEY=your_mistral_ai_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
GROQ_API_KEY=your_groq_api_key_here
```

### 3. n8n'de Kimlik Bilgilerini Oluşturun

#### Google Gemini Credential:
- **Type:** Google Gemini
- **Name:** `google_gemini`
- **API Key:** Google AI Studio'dan aldığınız anahtar

#### Mistral AI Credential:
- **Type:** Mistral AI
- **Name:** `mistral_ai`
- **API Key:** Mistral Console'dan aldığınız anahtar

#### OpenRouter Credential (Opsiyonel):
- **Type:** OpenRouter
- **Name:** `openrouter_ai`
- **API Key:** OpenRouter'dan aldığınız anahtar

### 4. Yeni Workflow'ları Aktifleştirin

1. n8n arayüzünde "Workflows" sekmesine gidin
2. Aşağıdaki workflow'ları bulun ve aktifleştirin:
   - **📰 Sentiment Analyst (Free AI)**
   - **🧠 Master Strategist (Free AI)**

3. Eski AI workflow'larını deaktifleştirin:
   - **📰 Sentiment & News Analyst Agent** (OpenAI kullanan)
   - **🧠 Master Strategist Brain** (OpenAI kullanan)

## 🧪 Test Etme

### 1. Google Gemini Testi:
```bash
curl -X POST \
  'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "contents": [{
      "parts": [{
        "text": "Merhaba, nasılsın?"
      }]
    }]
  }'
```

### 2. Workflow Test:
1. Telegram bot'unuza `/analiz` komutunu gönderin
2. Sistem yeni AI provider'ları kullanarak analiz yapmalı
3. Türkçe yanıtlar almalısınız

## 💰 Maliyet Karşılaştırması

| Provider | Ücretsiz Limit | Aylık Maliyet |
|----------|----------------|---------------|
| Google Gemini | 1,500 istek/gün | $0 |
| Mistral AI | $5 kredi/ay | $0 |
| OpenRouter | Değişken | $0-5 |
| Groq | 14,400 token/gün | $0 |
| **OpenAI GPT-4** | **Yok** | **$20-100+** |

## 🔄 Geçiş Stratejisi

### Aşama 1: Paralel Çalıştırma (1 hafta)
- Hem eski hem yeni workflow'ları aktif tutun
- Sonuçları karşılaştırın
- Performansı izleyin

### Aşama 2: Tam Geçiş
- Eski OpenAI workflow'larını deaktifleştirin
- Sadece ücretsiz AI provider'ları kullanın
- OpenAI credential'ını silin

### Aşama 3: Optimizasyon
- En iyi performans gösteren provider'ı ana olarak seçin
- Diğerlerini fallback olarak kullanın
- Rate limit'leri optimize edin

## 🚨 Önemli Notlar

### Rate Limit Yönetimi:
- Google Gemini: Günde 1,500 istek (sistem günde ~500 kullanır)
- Mistral: Aylık $5 kredi (sistem aylık ~$2-3 kullanır)
- Fallback sistemi otomatik çalışır

### Kalite Karşılaştırması:
- **Google Gemini:** OpenAI'ya çok yakın kalite
- **Mistral:** Teknik analizde güçlü
- **OpenRouter:** Model çeşitliliği
- **Groq:** Çok hızlı ama bazen daha basit

### Türkçe Desteği:
- Google Gemini: Mükemmel
- Mistral: İyi
- OpenRouter: Model'e bağlı
- Groq: Orta

## 🎉 Sonuç

Bu güncelleme ile:
✅ **Aylık $0 AI maliyeti** (OpenAI yerine)  
✅ **Aynı kalitede analizler** (Google Gemini ile)  
✅ **Daha hızlı yanıtlar** (Groq ile)  
✅ **Çoklu provider desteği** (Fallback sistemi)  
✅ **Türkçe optimizasyonu** (Yerel dil desteği)  

Artık sisteminiz tamamen ücretsiz AI provider'larla çalışıyor! 🚀

---

**💡 İpucu:** Google Gemini'nin günlük limitini aşarsanız, sistem otomatik olarak Mistral'a geçer. Bu sayede kesintisiz hizmet alırsınız.
