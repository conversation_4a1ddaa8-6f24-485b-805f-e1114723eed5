{"agent_id": "data_harvester_agent", "agent_name": "Data Harvester Specialist", "version": "2.0.0", "description": "Kripto para veri toplama ve normalizasyon uzmanı", "model_config": {"primary_model": "gemini-1.5-flash", "fallback_model": "mistral-small", "temperature": 0.2, "max_tokens": 2048, "top_p": 0.9, "frequency_penalty": 0.1, "presence_penalty": 0.1}, "system_prompt": "Sen bir kripto para veri toplama uzmanısın. G<PERSON><PERSON>vin çeşitli kaynaklardan gelen ham verileri analiz etmek ve standardize etmektir.\n\nGÖREVLER:\n1. Binance API verilerini işle ve normalize et\n2. CoinGecko market verilerini analiz et\n3. NewsAPI haberlerini kategorize et\n4. Fear & Greed Index verilerini değerlendir\n5. Veri kalitesini kontrol et ve anomalileri tespit et\n\nÇIKTI FORMATI:\n{\n  \"data_quality_score\": 0.95,\n  \"processed_symbols\": [\"BTCUSDT\", \"ETHUSDT\"],\n  \"anomalies_detected\": [],\n  \"data_freshness\": \"excellent\",\n  \"next_collection_priority\": [\"volume_spike\", \"price_movement\"],\n  \"summary\": \"<PERSON>ısa özet\"\n}\n\nKURALLAR:\n- Her zaman JSON formatında yanıt ver\n- Veri kalitesi skorunu 0-1 arasında belirt\n- Anomalileri mutlaka raporla\n- Türkçe açıklamalar kullan", "tools": [{"name": "data_validator", "description": "<PERSON>eri ka<PERSON> kontrolü", "parameters": {"validation_rules": ["price_range", "volume_threshold", "timestamp_check"]}}, {"name": "anomaly_detector", "description": "Anomali tespit sistemi", "parameters": {"sensitivity": 0.8, "lookback_period": "24h"}}], "memory_config": {"short_term_memory": 50, "long_term_memory": 500, "context_retention": "12h", "learning_enabled": true, "pattern_recognition": true}, "data_sources": [{"name": "binance_api", "endpoint": "https://api.binance.com/api/v3/ticker/24hr", "rate_limit": "1200/minute", "priority": "high"}, {"name": "coingecko_api", "endpoint": "https://api.coingecko.com/api/v3/coins/markets", "rate_limit": "50/minute", "priority": "medium"}, {"name": "newsapi", "endpoint": "https://newsapi.org/v2/everything", "rate_limit": "1000/day", "priority": "medium"}, {"name": "fear_greed_index", "endpoint": "https://api.alternative.me/fng/", "rate_limit": "unlimited", "priority": "low"}], "validation_rules": {"price_validation": {"min_price": 1e-06, "max_price": 1000000, "price_change_threshold": 0.5}, "volume_validation": {"min_volume": 0, "volume_spike_threshold": 3.0}, "timestamp_validation": {"max_age_minutes": 5, "future_tolerance_seconds": 30}}, "output_schema": {"type": "object", "required": ["data_quality_score", "processed_symbols", "summary"], "properties": {"data_quality_score": {"type": "number", "minimum": 0, "maximum": 1}, "processed_symbols": {"type": "array", "items": {"type": "string"}}, "anomalies_detected": {"type": "array", "items": {"type": "string"}}, "data_freshness": {"type": "string", "enum": ["excellent", "good", "fair", "poor"]}, "next_collection_priority": {"type": "array", "items": {"type": "string"}}, "summary": {"type": "string", "maxLength": 500}}}, "performance_metrics": {"target_response_time": "3s", "target_accuracy": 0.95, "target_uptime": 0.99, "error_threshold": 0.05}, "scheduling": {"frequency": "1m", "retry_attempts": 3, "retry_delay": "30s", "timeout": "30s"}, "alerts": {"data_quality_threshold": 0.8, "anomaly_alert": true, "performance_alert": true, "error_alert": true}}