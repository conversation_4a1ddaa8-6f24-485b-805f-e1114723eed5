{"agent_id": "telegram_herald_agent", "agent_name": "Telegram Communication Specialist", "version": "2.0.0", "description": "Kullanıcı iletişimi ve Telegram bot uzmanı", "model_config": {"primary_model": "gemini-1.5-flash", "fallback_model": "mistral-small", "temperature": 0.4, "max_tokens": 1024, "top_p": 0.9, "frequency_penalty": 0.2, "presence_penalty": 0.2}, "system_prompt": "Sen bir kripto para trading asistan<PERSON>s<PERSON>n. Görevin kullanıcılarla Telegram üzerinden etkileşim kurmak ve trading tavsiyelerini iletmektir.\n\nİLETİŞİM STİLİ:\n- Samimi ve profesyonel\n- Türkçe iletişim\n- <PERSON><PERSON><PERSON> (ölçülü)\n- Net ve anlaşılır açıklamalar\n\nÇIKTI FORMATI:\n{\n  \"message_type\": \"ALERT|REPORT|RESPONSE|NOTIFICATION\",\n  \"priority\": \"LOW|MEDIUM|HIGH|URGENT\",\n  \"telegram_message\": \"Kullanıcıya gönderilecek mesaj\",\n  \"include_chart\": true,\n  \"action_buttons\": [\n    {\"text\": \"📊 Detaylı Analiz\", \"callback\": \"detailed_analysis\"},\n    {\"text\": \"⚠️ Risk Raporu\", \"callback\": \"risk_report\"}\n  ],\n  \"follow_up_time\": \"2025-01-21T16:00:00Z\",\n  \"user_context\": \"Kullanıcının mevcut durumu\"\n}\n\nMESAJ ÖRNEKLERİ:\n- Fırsat bildirimi: \"🚀 BTC'de güçlü alım fırsatı! Target: $45,000\"\n- Risk uyarısı: \"⚠️ Yüksek volatilite bekleniyor, pozisyon boyutunu azaltın\"\n- Günlük rapor: \"📊 Bugünün piyasa özeti ve yarınki beklentiler\"\n\nKURALLAR:\n- Finansal tavsiye değil, bilgi paylaşımı\n- Risk uyarılarını unutma\n- Kullanıcı seviyesine uygun dil kullan\n- Acil durumları öncelikle", "tools": [{"name": "message_formatter", "description": "Telegram mesaj <PERSON>lama sistemi", "parameters": {"formats": ["markdown", "html"], "emoji_sets": ["crypto", "trading", "alerts"], "max_length": 4096}}, {"name": "chart_generator", "description": "Grafik ve chart oluşturma", "parameters": {"chart_types": ["price", "volume", "indicators", "portfolio"], "timeframes": ["1h", "4h", "1d"], "indicators": ["rsi", "macd", "bb"]}}], "memory_config": {"short_term_memory": 100, "long_term_memory": 1000, "context_retention": "24h", "learning_enabled": true, "user_preferences": true, "conversation_history": true}, "message_types": {"alerts": {"trading_opportunity": {"template": "🚀 {symbol} için {action} fırsatı!\n💰 Target: ${target_price}\n⚠️ Stop Loss: ${stop_loss}\n📊 Güven: {confidence}%", "priority": "HIGH", "include_chart": true}, "risk_warning": {"template": "⚠️ Risk Uyarısı!\n📉 {symbol} için yüksek risk tespit edildi\n🛡️ Önerilen aksiyon: {action}\n📊 Risk seviyesi: {risk_level}", "priority": "URGENT", "include_chart": false}}, "reports": {"daily_summary": {"template": "📊 Günlük Piyasa Özeti\n\n📈 En iyi performans: {top_performer}\n📉 En kötü performans: {worst_performer}\n💡 <PERSON><PERSON><PERSON><PERSON><PERSON> beklenti: {tomorrow_outlook}", "priority": "MEDIUM", "schedule": "daily_8am"}, "weekly_analysis": {"template": "📅 Haftalık Analiz Raporu\n\n📊 Haftalık performans: {weekly_performance}\n🎯 Gelecek hafta hedefleri: {next_week_targets}\n⚠️ Dikkat edilecek riskler: {risks}", "priority": "MEDIUM", "schedule": "weekly_sunday"}}}, "user_interaction": {"commands": {"/start": {"response": "Merhaba! 👋 Kripto Trading Asistanınıza hoş geldiniz!\n\n🤖 Size nasıl yardımcı olabilirim?\n📊 /analiz - Son piyasa analizi\n📈 /durum - Sistem durumu\n❓ /yardim - <PERSON><PERSON><PERSON> listesi", "buttons": ["📊 Analiz", "📈 Durum", "❓ Yardım"]}, "/analiz": {"response": "📊 Son piyasa analizi hazırlanıyor...", "action": "get_latest_analysis", "buttons": ["🔄 <PERSON><PERSON><PERSON>", "📈 Detay", "⚠️ Riskler"]}, "/durum": {"response": "🔍 Sistem durumu kontrol ediliyor...", "action": "get_system_status", "buttons": ["🔄 <PERSON><PERSON><PERSON>", "📊 <PERSON><PERSON><PERSON>", "🛠️ <PERSON><PERSON><PERSON>"]}}, "callbacks": {"detailed_analysis": "get_detailed_market_analysis", "risk_report": "get_risk_assessment", "portfolio_status": "get_portfolio_summary", "system_metrics": "get_system_metrics"}}, "notification_settings": {"trading_opportunities": {"enabled": true, "min_confidence": 0.8, "min_risk_reward": 2.0, "cooldown_minutes": 30}, "risk_alerts": {"enabled": true, "risk_threshold": 0.7, "immediate_send": true}, "market_updates": {"enabled": true, "frequency": "4h", "significant_moves_only": true, "threshold_percentage": 5.0}, "system_status": {"enabled": true, "error_alerts": true, "performance_alerts": true}}, "personalization": {"user_preferences": {"risk_tolerance": "medium", "preferred_timeframes": ["4h", "1d"], "notification_frequency": "normal", "language": "turkish"}, "adaptive_messaging": {"experience_level": "beginner|intermediate|advanced", "communication_style": "formal|casual|technical", "emoji_usage": "minimal|normal|extensive"}}, "content_generation": {"market_analysis": {"sections": ["price_action", "technical_indicators", "sentiment", "risk_factors"], "max_length": 2000, "include_charts": true}, "trading_signals": {"format": "structured", "include_reasoning": true, "risk_disclosure": true}, "educational_content": {"topics": ["technical_analysis", "risk_management", "market_psychology"], "difficulty_levels": ["beginner", "intermediate", "advanced"]}}, "safety_features": {"disclaimer": {"include_always": true, "text": "⚠️ Bu bilgiler yatırım tavsiyesi değildir. Kendi araştırmanızı yapın ve risk yönetimini unutmayın."}, "rate_limiting": {"max_messages_per_minute": 10, "max_alerts_per_hour": 5, "cooldown_period": "5m"}, "content_filtering": {"spam_detection": true, "inappropriate_content": true, "financial_advice_warning": true}}, "analytics": {"user_engagement": {"track_interactions": true, "response_times": true, "user_satisfaction": true}, "message_effectiveness": {"click_through_rates": true, "action_completion": true, "feedback_scores": true}}, "output_schema": {"type": "object", "required": ["message_type", "priority", "telegram_message"], "properties": {"message_type": {"type": "string", "enum": ["ALERT", "REPORT", "RESPONSE", "NOTIFICATION"]}, "priority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "URGENT"]}, "telegram_message": {"type": "string", "maxLength": 4096}}}, "performance_metrics": {"target_response_time": "2s", "target_accuracy": 0.9, "target_uptime": 0.99, "error_threshold": 0.02}, "scheduling": {"frequency": "realtime", "retry_attempts": 5, "retry_delay": "10s", "timeout": "15s"}}