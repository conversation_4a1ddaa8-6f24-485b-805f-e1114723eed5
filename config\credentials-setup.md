# n8n <PERSON><PERSON> Bilgileri Kurulum Rehberi

B<PERSON>, Multi-Agent AI Trading Assistant projesi için gerekli tüm kimlik bilgilerinin n8n'de nasıl kurulacağı açıklanmaktadır.

## 🔐 Gerekli API Anahtarları

### 1. PostgreSQL Veritabanı
- **Credential Type:** Postgres
- **Credential Name:** `crypto_trading_db`
- **Host:** localhost (veya Docker container adı)
- **Database:** crypto_trading_ai
- **User:** n8n_user
- **Password:** [gü<PERSON><PERSON> şifre]
- **Port:** 5432

### 2. Binance API
- **Credential Type:** Generic Credential
- **Credential Name:** `binance_api`
- **Fields:**
  - `api_key`: Binance API Key
  - `api_secret`: Binance API Secret
- **Kaynak:** https://www.binance.com/en/my/settings/api-management

### 3. CoinGecko API
- **Credential Type:** Generic Credential  
- **Credential Name:** `coingecko_api`
- **Fields:**
  - `api_key`: CoinGecko Pro API Key (opsiyonel, rate limit için)
- **Kaynak:** https://www.coingecko.com/en/api/pricing

### 4. NewsAPI
- **Credential Type:** Generic Credential
- **Credential Name:** `newsapi_key`
- **Fields:**
  - `api_key`: NewsAPI Key
- **Kaynak:** https://newsapi.org/register

### 5. AI Provider'lar (Ücretsiz Alternatifler)

#### 5a. Google Gemini API (ÖNERİLEN - Ücretsiz)
- **Credential Type:** Google Gemini
- **Credential Name:** `google_gemini`
- **API Key:** Google AI Studio API Key
- **Kaynak:** https://aistudio.google.com/app/apikey
- **Ücretsiz Limit:** Günde 1500 istek

#### 5b. Mistral AI (Ücretsiz Tier)
- **Credential Type:** Mistral AI
- **Credential Name:** `mistral_ai`
- **API Key:** Mistral AI API Key
- **Kaynak:** https://console.mistral.ai/
- **Ücretsiz Limit:** Aylık $5 kredi

#### 5c. OpenRouter (Çoklu Model Erişimi)
- **Credential Type:** OpenRouter
- **Credential Name:** `openrouter_ai`
- **API Key:** OpenRouter API Key
- **Kaynak:** https://openrouter.ai/keys
- **Ücretsiz Modeller:** Llama, Mistral, Gemma

#### 5d. Groq (Hızlı Inference)
- **Credential Type:** Groq
- **Credential Name:** `groq_ai`
- **API Key:** Groq API Key
- **Kaynak:** https://console.groq.com/keys
- **Ücretsiz Limit:** Günde 14,400 token

#### 5e. OpenAI (Opsiyonel - Ücretli)
- **Credential Type:** OpenAI
- **Credential Name:** `openai_gpt4`
- **API Key:** OpenAI API Key
- **Kaynak:** https://platform.openai.com/api-keys
- **Not:** Sadece ücretli kullanım istiyorsanız

### 6. Telegram Bot
- **Credential Type:** Generic Credential
- **Credential Name:** `telegram_bot`
- **Fields:**
  - `bot_token`: Telegram Bot Token
  - `chat_id`: Kullanıcı Chat ID
- **Bot Oluşturma:** @BotFather ile konuşarak yeni bot oluşturun

### 7. Alternative.me (Fear & Greed Index)
- **Credential Type:** Yok (Public API)
- **URL:** https://api.alternative.me/fng/

## 🐳 Docker Compose Kurulumu

PostgreSQL'i Docker ile çalıştırmak için:

```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: crypto_trading_ai
      POSTGRES_USER: n8n_user
      POSTGRES_PASSWORD: your_secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    restart: unless-stopped

volumes:
  postgres_data:
```

## 📋 Kurulum Adımları

1. **PostgreSQL'i başlatın:**
   ```bash
   docker-compose up -d postgres
   ```

2. **n8n'de kimlik bilgilerini oluşturun:**
   - n8n arayüzünde Settings > Credentials'a gidin
   - Yukarıdaki her kimlik bilgisi için yeni credential oluşturun
   - Test butonlarını kullanarak bağlantıları doğrulayın

3. **Veritabanı şemasını yükleyin:**
   ```bash
   docker exec -i postgres_container psql -U n8n_user -d crypto_trading_ai < database/schema.sql
   ```

## 🔒 Güvenlik Notları

- API anahtarlarını asla kod içinde saklamayın
- n8n credential manager'ı güvenli şifreleme kullanır
- Binance API için sadece "Read" yetkisi verin (trading için ayrı setup gerekli)
- Telegram bot token'ını kimseyle paylaşmayın
- PostgreSQL şifresini güçlü tutun

## 🧪 Test Komutları

### PostgreSQL Bağlantı Testi:
```sql
SELECT version();
SELECT * FROM agent_status;
```

### Binance API Testi:
```bash
curl -X GET 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT'
```

### CoinGecko API Testi:
```bash
curl -X GET 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd'
```

### NewsAPI Testi:
```bash
curl -X GET 'https://newsapi.org/v2/everything?q=cryptocurrency&apiKey=YOUR_API_KEY'
```

## 📞 Destek

Herhangi bir sorun yaşarsanız:
1. n8n loglarını kontrol edin
2. API rate limitlerini kontrol edin  
3. Kimlik bilgilerinin doğru girildiğini doğrulayın
4. Network bağlantısını test edin
