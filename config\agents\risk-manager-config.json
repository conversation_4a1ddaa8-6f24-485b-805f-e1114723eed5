{"agent_id": "risk_manager_agent", "agent_name": "Risk Management Expert", "version": "2.0.0", "description": "Kripto para risk yönetimi ve portföy koruma uzmanı", "model_config": {"primary_model": "gemini-1.5-pro", "fallback_model": "mistral-medium", "temperature": 0.1, "max_tokens": 2048, "top_p": 0.9, "frequency_penalty": 0.1, "presence_penalty": 0.1}, "system_prompt": "Sen bir kripto para risk yönetimi uzmanısın. Görevin portföy risklerini değerlendirmek ve koruyucu önlemler önermektir.\n\nRİSK DEĞERLENDİRME ALANLARI:\n1. Volatilite analizi\n2. Korelasyon riskleri\n3. Likidite riskleri\n4. Makroekonomik riskler\n5. <PERSON>k<PERSON> riskler (support kırılması, etc.)\n\nÇIKTI FORMATI:\n{\n  \"overall_risk_level\": \"LOW|MEDIUM|HIGH|CRITICAL\",\n  \"risk_score\": 0.65,\n  \"volatility_assessment\": {\n    \"current_volatility\": 0.45,\n    \"expected_volatility\": 0.52,\n    \"volatility_trend\": \"INCREASING\"\n  },\n  \"position_recommendations\": {\n    \"max_position_size\": 0.15,\n    \"stop_loss_levels\": {\n      \"conservative\": 0.05,\n      \"moderate\": 0.08,\n      \"aggressive\": 0.12\n    }\n  },\n  \"risk_factors\": [\n    \"high_correlation_btc_eth\",\n    \"low_weekend_liquidity\"\n  ],\n  \"hedging_suggestions\": [\"reduce_leverage\", \"diversify_timeframes\"],\n  \"alert_triggers\": {\n    \"volatility_spike\": 0.6,\n    \"correlation_break\": 0.3\n  },\n  \"reasoning\": \"Risk analizi detayları\"\n}\n\nKURALLAR:\n- Konservatif yaklaşım benimse\n- Worst-case senaryoları düşün\n- Likidite risklerini unutma\n- Korelasyonları takip et", "tools": [{"name": "risk_calculator", "description": "Gelişmiş risk hesap<PERSON>a sistemi", "parameters": {"metrics": ["var", "cvar", "sharpe_ratio", "max_drawdown", "beta"], "confidence_levels": [0.95, 0.99], "lookback_periods": ["1d", "7d", "30d"]}}, {"name": "correlation_analyzer", "description": "Varlık korelasyon analizi", "parameters": {"correlation_methods": ["pearson", "spearman", "kendall"], "rolling_windows": [24, 168, 720], "threshold_alerts": 0.8}}], "memory_config": {"short_term_memory": 150, "long_term_memory": 1500, "context_retention": "72h", "learning_enabled": true, "pattern_recognition": true}, "risk_metrics": {"volatility": {"calculation_method": "rolling_std", "window_size": 24, "annualization_factor": 365, "thresholds": {"low": 0.3, "medium": 0.5, "high": 0.7, "critical": 1.0}}, "value_at_risk": {"confidence_levels": [0.95, 0.99], "methods": ["historical", "parametric", "monte_carlo"], "holding_period": 1, "lookback_period": 252}, "correlation": {"calculation_window": 30, "update_frequency": "1h", "alert_thresholds": {"high_correlation": 0.8, "correlation_break": 0.3}}}, "position_sizing": {"methods": {"kelly_criterion": {"enabled": true, "max_allocation": 0.25, "min_win_rate": 0.55}, "fixed_percentage": {"enabled": true, "percentage": 0.02, "max_positions": 10}, "volatility_adjusted": {"enabled": true, "target_volatility": 0.15, "lookback_period": 30}}, "risk_limits": {"max_single_position": 0.2, "max_sector_exposure": 0.4, "max_daily_loss": 0.05, "max_drawdown": 0.15}}, "liquidity_assessment": {"metrics": {"bid_ask_spread": {"threshold_warning": 0.005, "threshold_critical": 0.02}, "market_depth": {"min_depth_usd": 100000, "depth_levels": [0.1, 0.5, 1.0]}, "volume_analysis": {"min_24h_volume": 1000000, "volume_trend_periods": [1, 7, 30]}}, "time_factors": {"weekend_penalty": 0.2, "holiday_penalty": 0.3, "asian_hours_penalty": 0.1}}, "stress_testing": {"scenarios": {"market_crash": {"btc_drop": -0.3, "alt_multiplier": 1.5, "correlation_increase": 0.2}, "liquidity_crisis": {"spread_increase": 3.0, "volume_decrease": -0.5, "slippage_increase": 2.0}, "regulatory_shock": {"immediate_drop": -0.15, "volatility_spike": 2.0, "recovery_time": "7d"}}, "monte_carlo": {"simulations": 10000, "time_horizon": 30, "confidence_intervals": [0.05, 0.25, 0.5, 0.75, 0.95]}}, "alert_system": {"risk_level_changes": {"enabled": true, "threshold_change": 0.2}, "volatility_spikes": {"enabled": true, "spike_threshold": 2.0, "lookback_hours": 4}, "correlation_breaks": {"enabled": true, "break_threshold": 0.3, "monitoring_pairs": ["BTC-ETH", "BTC-ALT"]}, "liquidity_warnings": {"enabled": true, "spread_threshold": 0.01, "volume_drop_threshold": -0.3}}, "output_schema": {"type": "object", "required": ["overall_risk_level", "risk_score", "reasoning"], "properties": {"overall_risk_level": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "risk_score": {"type": "number", "minimum": 0, "maximum": 1}, "volatility_assessment": {"type": "object", "properties": {"current_volatility": {"type": "number"}, "expected_volatility": {"type": "number"}, "volatility_trend": {"type": "string", "enum": ["INCREASING", "DECREASING", "STABLE"]}}}}}, "performance_metrics": {"target_response_time": "6s", "target_accuracy": 0.85, "target_uptime": 0.99, "error_threshold": 0.03}, "scheduling": {"frequency": "10m", "retry_attempts": 3, "retry_delay": "90s", "timeout": "60s"}}