{"agent_id": "technical_analyst_agent", "agent_name": "Technical Analysis Expert", "version": "2.0.0", "description": "Kripto para teknik analiz ve chart pattern uzmanı", "model_config": {"primary_model": "gemini-1.5-pro", "fallback_model": "mistral-medium", "temperature": 0.1, "max_tokens": 3072, "top_p": 0.9, "frequency_penalty": 0.1, "presence_penalty": 0.1}, "system_prompt": "Sen bir kripto para teknik analiz uzmanısın. G<PERSON><PERSON>vin fiyat ha<PERSON>, indikatörleri ve chart pattern'lerini analiz etmektir.\n\nUZMANLIK ALANLARIN:\n- RSI, MACD, Bollinger Bands analizi\n- Support/Resistance seviyeleri\n- Chart pattern tanıma (Head & Shoulders, Triangles, etc.)\n- Volume analizi\n- Fibonacci retracement seviyeleri\n\nÇIKTI FORMATI:\n{\n  \"symbol\": \"BTCUSDT\",\n  \"trend_direction\": \"BULLISH|BEARISH|SIDEWAYS\",\n  \"strength\": 0.75,\n  \"key_levels\": {\n    \"support\": [42000, 41500],\n    \"resistance\": [45000, 46200]\n  },\n  \"indicators\": {\n    \"rsi\": {\"value\": 65, \"signal\": \"NEUTRAL\"},\n    \"macd\": {\"signal\": \"BULLISH\", \"strength\": 0.8}\n  },\n  \"patterns_detected\": [\"ascending_triangle\"],\n  \"time_horizon\": \"4h\",\n  \"confidence\": 0.82,\n  \"recommendation\": \"HOLD\",\n  \"reasoning\": \"Detaylı analiz açıklaması\"\n}\n\nKURALLAR:\n- Sadece güvenilir sinyaller ver\n- Confidence skorunu realistik tut\n- Pattern'leri doğru tanımla\n- Risk seviyelerini belirt", "tools": [{"name": "chart_analyzer", "description": "Chart pattern tan<PERSON>ma sistemi", "parameters": {"timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"], "patterns": ["triangle", "head_shoulders", "flag", "wedge", "channel"]}}, {"name": "indicator_calculator", "description": "Teknik indikatör he<PERSON>a", "parameters": {"indicators": ["rsi", "macd", "bb", "ema", "sma", "<PERSON><PERSON><PERSON><PERSON>"], "periods": {"rsi": 14, "macd": [12, 26, 9], "bb": 20}}}], "memory_config": {"short_term_memory": 100, "long_term_memory": 1000, "context_retention": "24h", "learning_enabled": true, "pattern_recognition": true}, "analysis_parameters": {"timeframes": {"primary": "4h", "secondary": ["1h", "1d"], "scalping": ["5m", "15m"]}, "indicators": {"rsi": {"period": 14, "overbought": 70, "oversold": 30}, "macd": {"fast": 12, "slow": 26, "signal": 9}, "bollinger_bands": {"period": 20, "std_dev": 2}, "fibonacci": {"levels": [0.236, 0.382, 0.5, 0.618, 0.786]}}, "patterns": {"min_confidence": 0.7, "lookback_periods": 50, "confirmation_candles": 3}}, "signal_generation": {"trend_signals": {"strong_bullish": {"min_confidence": 0.8, "min_indicators": 3}, "bullish": {"min_confidence": 0.6, "min_indicators": 2}, "neutral": {"min_confidence": 0.4, "min_indicators": 1}, "bearish": {"min_confidence": 0.6, "min_indicators": 2}, "strong_bearish": {"min_confidence": 0.8, "min_indicators": 3}}, "entry_signals": {"buy": ["rsi_oversold", "macd_bullish_cross", "support_bounce"], "sell": ["rsi_overbought", "macd_bearish_cross", "resistance_rejection"]}}, "risk_parameters": {"stop_loss": {"atr_multiplier": 2.0, "percentage_based": 0.05, "support_resistance_based": true}, "take_profit": {"risk_reward_ratio": 2.0, "fibonacci_targets": true, "resistance_levels": true}}, "output_schema": {"type": "object", "required": ["symbol", "trend_direction", "confidence", "recommendation"], "properties": {"symbol": {"type": "string"}, "trend_direction": {"type": "string", "enum": ["BULLISH", "BEARISH", "SIDEWAYS"]}, "strength": {"type": "number", "minimum": 0, "maximum": 1}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}, "recommendation": {"type": "string", "enum": ["STRONG_BUY", "BUY", "HOLD", "SELL", "STRONG_SELL"]}}}, "performance_metrics": {"target_response_time": "5s", "target_accuracy": 0.75, "target_uptime": 0.99, "error_threshold": 0.05}, "scheduling": {"frequency": "5m", "retry_attempts": 3, "retry_delay": "60s", "timeout": "45s"}}